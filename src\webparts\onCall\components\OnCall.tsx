import * as React from 'react';
import styles from './OnCall.module.scss';
import type { IOnCallProps } from './IOnCallProps';
import {
  DatePicker,
  DetailsList,
  IColumn,
  SelectionMode,
  MessageBar,
  MessageBarType,
  Stack,
  Text,
  Spinner,
  SpinnerSize,
  Dropdown,
  IDropdownOption,
  IconButton,
  Label,
  SearchBox,
  DefaultButton,
  PrimaryButton,
  Dialog,
  DialogType,
  DialogFooter,
  StackItem,
  IStackTokens,
  IStackStyles,
} from '@fluentui/react';
import { OnCallScheduleService } from '../services/OnCallScheduleService';
import { IOnCallScheduleItem, IDateRange } from './IOnCallScheduleItem';
import { OnCallFormPanel } from './OnCallFormPanel';

interface ISortState {
  key: string;
  isDescending: boolean;
}

interface IOnCallState {
  scheduleItems: IOnCallScheduleItem[];
  filteredItems: IOnCallScheduleItem[];
  paginatedItems: IOnCallScheduleItem[];
  dateRange: IDateRange;
  searchTerm: string;
  isLoading: boolean;
  errorMessage: string;
  dateValidationError: string;
  currentPage: number;
  pageSize: number;
  totalPages: number;
  sortState: ISortState;
  isFiltersExpanded: boolean;
  isFormPanelOpen: boolean;
  editingItem: IOnCallScheduleItem | undefined;
  isFormLoading: boolean;
  operationMessage: string;
  operationMessageType: MessageBarType;
  showDeleteDialog: boolean;
  itemToDelete: IOnCallScheduleItem | undefined;
}

export default class OnCall extends React.Component<IOnCallProps, IOnCallState> {
  private onCallService: OnCallScheduleService;
  private columns: IColumn[];

  constructor(props: IOnCallProps) {
    super(props);

    this.onCallService = new OnCallScheduleService(props.spHttpClient, props.webUrl, props.listName, props.context);

    // Initialize state
    this.state = {
      scheduleItems: [],
      filteredItems: [],
      paginatedItems: [],
      dateRange: {
        startDate: undefined,
        endDate: undefined
      },
      searchTerm: '',
      isLoading: false,
      errorMessage: '',
      dateValidationError: '',
      currentPage: 1,
      pageSize: 10,
      totalPages: 0,
      sortState: {
        key: 'title',
        isDescending: false
      },
      isFiltersExpanded: false,
      isFormPanelOpen: false,
      editingItem: undefined,
      isFormLoading: false,
      operationMessage: '',
      operationMessageType: MessageBarType.success,
      showDeleteDialog: false,
      itemToDelete: undefined
    };

    // Define table columns with sorting
    this.columns = [
      {
        key: 'title',
        name: 'Title',
        fieldName: 'Title',
        minWidth: 150,
        maxWidth: 200,
        isResizable: true,
        isSorted: true,
        isSortedDescending: false,
        onColumnClick: this.onColumnClick,
        onRender: (item: IOnCallScheduleItem) => (
          <span>{item.Title || ''}</span>
        )
      },
      {
        key: 'manager',
        name: 'Manager',
        fieldName: 'Manager',
        minWidth: 120,
        maxWidth: 150,
        isResizable: true,
        onColumnClick: this.onColumnClick,
        onRender: (item: IOnCallScheduleItem) => (
          <span>{item.Manager?.Title || ''}</span>
        )
      },
      {
        key: 'managerMobile',
        name: 'Manager Mobile',
        fieldName: 'ManagerMobile',
        minWidth: 120,
        maxWidth: 150,
        isResizable: true,
        onColumnClick: this.onColumnClick,
        onRender: (item: IOnCallScheduleItem) => (
          <span>{item.ManagerMobile || ''}</span>
        )
      },
      {
        key: 'primaryContact',
        name: 'Primary Contact',
        fieldName: 'PrimaryContact',
        minWidth: 120,
        maxWidth: 150,
        isResizable: true,
        onColumnClick: this.onColumnClick,
        onRender: (item: IOnCallScheduleItem) => (
          <span>{item.PrimaryContact?.Title || ''}</span>
        )
      },
      {
        key: 'primaryContactMobile',
        name: 'Primary Mobile',
        fieldName: 'PrimaryContactMobile',
        minWidth: 120,
        maxWidth: 150,
        isResizable: true,
        onColumnClick: this.onColumnClick,
        onRender: (item: IOnCallScheduleItem) => (
          <span>{item.PrimaryContactMobile || ''}</span>
        )
      },
      {
        key: 'queueManager',
        name: 'Secondary Contact',
        fieldName: 'QueueManager',
        minWidth: 120,
        maxWidth: 150,
        isResizable: true,
        onColumnClick: this.onColumnClick,
        onRender: (item: IOnCallScheduleItem) => (
          <span>{item.QueueManager?.Title || ''}</span>
        )
      },
      {
        key: 'queueManagerMobile',
        name: 'Secondary Mobile',
        fieldName: 'QueueManagerMobile',
        minWidth: 120,
        maxWidth: 150,
        isResizable: true,
        onColumnClick: this.onColumnClick,
        onRender: (item: IOnCallScheduleItem) => (
          <span>{item.QueueManagerMobile || ''}</span>
        )
      },
      {
        key: 'startDate',
        name: 'Start Date',
        fieldName: 'StartDate',
        minWidth: 120,
        maxWidth: 150,
        isResizable: true,
        onColumnClick: this.onColumnClick,
        onRender: (item: IOnCallScheduleItem) => (
          <span>{item.StartDate ? OnCallScheduleService.formatDateOnly(item.StartDate) : ''}</span>
        )
      },
      {
        key: 'endDate',
        name: 'End Date',
        fieldName: 'EndDate',
        minWidth: 120,
        maxWidth: 150,
        isResizable: true,
        onColumnClick: this.onColumnClick,
        onRender: (item: IOnCallScheduleItem) => (
          <span>{item.EndDate ? OnCallScheduleService.formatDateOnly(item.EndDate) : ''}</span>
        )
      },
      {
        key: 'actions',
        name: 'Actions',
        fieldName: 'actions',
        minWidth: 100,
        maxWidth: 120,
        isResizable: false,
        onRender: (item: IOnCallScheduleItem) => (
          <Stack horizontal tokens={{ childrenGap: 8 }}>
            <IconButton
              iconProps={{ iconName: 'Edit' }}
              title="Edit"
              ariaLabel="Edit item"
              onClick={() => this.openEditForm(item)}
              styles={{
                root: { color: '#0078d4' },
                rootHovered: { backgroundColor: '#f3f2f1' }
              }}
            />
            <IconButton
              iconProps={{ iconName: 'Delete' }}
              title="Delete"
              ariaLabel="Delete item"
              onClick={() => this.openDeleteDialog(item)}
              styles={{
                root: { color: '#d13438' },
                rootHovered: { backgroundColor: '#f3f2f1' }
              }}
            />
          </Stack>
        )
      }
    ];
  }

  public componentDidMount(): void {
    console.log('OnCall component mounted');
    void this.loadScheduleData();
  }

  public componentDidUpdate(prevProps: IOnCallProps): void {
    // If the list name changed, update the service and reload data
    if (prevProps.listName !== this.props.listName) {
      this.onCallService = new OnCallScheduleService(this.props.spHttpClient, this.props.webUrl, this.props.listName, this.props.context);
      void this.loadScheduleData();
    }
  }

  private loadScheduleData = async (): Promise<void> => {
    this.setState({ isLoading: true, errorMessage: '' });

    try {
      const { startDate, endDate } = this.state.dateRange;
      const items = await this.onCallService.getOnCallScheduleItems(startDate, endDate);

      this.setState({
        scheduleItems: items,
        currentPage: 1,
        isLoading: false
      }, () => {
        this.applyFilters();
      });
    } catch (error) {
      console.error('Error loading schedule data:', error);
      this.setState({
        errorMessage: `Failed to load schedule data from list "${this.props.listName}". Please check if the list exists and you have access to it.`,
        isLoading: false
      });
    }
  };

  private applyFilters = (): void => {
    const { scheduleItems, searchTerm } = this.state;
    let filtered = [...scheduleItems];

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(item => {
        const title = item.Title?.toLowerCase() || '';
        const manager = item.Manager?.Title?.toLowerCase() || '';
        const primaryContact = item.PrimaryContact?.Title?.toLowerCase() || '';
        const queueManager = item.QueueManager?.Title?.toLowerCase() || '';

        return title.indexOf(searchLower) !== -1 ||
               manager.indexOf(searchLower) !== -1 ||
               primaryContact.indexOf(searchLower) !== -1 ||
               queueManager.indexOf(searchLower) !== -1;
      });
    }

    // Apply sorting
    filtered = this.sortItems(filtered);

    this.setState({
      filteredItems: filtered,
      currentPage: 1
    }, () => {
      this.updatePagination();
    });
  };

  private sortItems = (items: IOnCallScheduleItem[]): IOnCallScheduleItem[] => {
    const { sortState } = this.state;

    return items.sort((a, b) => {
      let aValue: string | Date;
      let bValue: string | Date;

      switch (sortState.key) {
        case 'title':
          aValue = a.Title || '';
          bValue = b.Title || '';
          break;
        case 'manager':
          aValue = a.Manager?.Title || '';
          bValue = b.Manager?.Title || '';
          break;
        case 'managerMobile':
          aValue = a.ManagerMobile || '';
          bValue = b.ManagerMobile || '';
          break;
        case 'primaryContact':
          aValue = a.PrimaryContact?.Title || '';
          bValue = b.PrimaryContact?.Title || '';
          break;
        case 'primaryContactMobile':
          aValue = a.PrimaryContactMobile || '';
          bValue = b.PrimaryContactMobile || '';
          break;
        case 'queueManager':
          aValue = a.QueueManager?.Title || '';
          bValue = b.QueueManager?.Title || '';
          break;
        case 'queueManagerMobile':
          aValue = a.QueueManagerMobile || '';
          bValue = b.QueueManagerMobile || '';
          break;
        case 'startDate':
          aValue = a.StartDate && new Date(a.StartDate).getFullYear() >= 1970 ? new Date(a.StartDate) : new Date(0);
          bValue = b.StartDate && new Date(b.StartDate).getFullYear() >= 1970 ? new Date(b.StartDate) : new Date(0);
          break;
        case 'endDate':
          aValue = a.EndDate && new Date(a.EndDate).getFullYear() >= 1970 ? new Date(a.EndDate) : new Date(0);
          bValue = b.EndDate && new Date(b.EndDate).getFullYear() >= 1970 ? new Date(b.EndDate) : new Date(0);
          break;
        default:
          aValue = a.Title || '';
          bValue = b.Title || '';
      }

      let result = 0;
      if (aValue < bValue) {
        result = -1;
      } else if (aValue > bValue) {
        result = 1;
      }

      return sortState.isDescending ? -result : result;
    });
  };

  private onColumnClick = (_event: React.MouseEvent<HTMLElement>, column: IColumn): void => {
    const { sortState } = this.state;
    const newSortState: ISortState = {
      key: column.key,
      isDescending: column.key === sortState.key ? !sortState.isDescending : false
    };

    // Update column sort indicators
    this.columns = this.columns.map(col => ({
      ...col,
      isSorted: col.key === column.key,
      isSortedDescending: col.key === column.key ? newSortState.isDescending : false
    }));

    this.setState({
      sortState: newSortState
    }, () => {
      this.applyFilters();
    });
  };

  private updatePagination = (): void => {
    const { filteredItems, currentPage, pageSize } = this.state;
    const totalPages = Math.ceil(filteredItems.length / pageSize);
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedItems = filteredItems.slice(startIndex, endIndex);

    this.setState({
      paginatedItems,
      totalPages
    });
  };

  private onPageSizeChange = (_event: React.FormEvent<HTMLDivElement>, option?: IDropdownOption): void => {
    if (option) {
      this.setState({
        pageSize: option.key as number,
        currentPage: 1
      }, () => {
        this.updatePagination();
      });
    }
  };

  private onPageChange = (newPage: number): void => {
    this.setState({
      currentPage: newPage
    }, () => {
      this.updatePagination();
    });
  };

  private onSearchChange = (_event?: React.ChangeEvent<HTMLInputElement>, newValue?: string): void => {
    this.setState({
      searchTerm: newValue || ''
    }, () => {
      this.applyFilters();
    });
  };

  private onClearSearch = (): void => {
    this.setState({
      searchTerm: ''
    }, () => {
      this.applyFilters();
    });
  };

  private onClearDates = (): void => {
    this.setState({
      dateRange: {
        startDate: undefined,
        endDate: undefined
      },
      dateValidationError: ''
    }, () => {
      void this.loadScheduleData();
    });
  };

  private toggleFiltersExpanded = (): void => {
    this.setState({
      isFiltersExpanded: !this.state.isFiltersExpanded
    });
  };

  // CRUD Operations
  private openAddForm = (): void => {
    this.setState({
      isFormPanelOpen: true,
      editingItem: undefined
    });
  };

  private openEditForm = (item: IOnCallScheduleItem): void => {
    this.setState({
      isFormPanelOpen: true,
      editingItem: item
    });
  };

  private closeForm = (): void => {
    this.setState({
      isFormPanelOpen: false,
      editingItem: undefined,
      isFormLoading: false
    });
  };

  private saveItem = async (item: Partial<IOnCallScheduleItem>): Promise<void> => {
    this.setState({ isFormLoading: true });

    try {
      if (this.state.editingItem) {
        // Update existing item
        await this.onCallService.updateOnCallScheduleItem(this.state.editingItem.Id, item);
        this.showOperationMessage('Item updated successfully', MessageBarType.success);
      } else {
        // Create new item
        await this.onCallService.createOnCallScheduleItem(item);
        this.showOperationMessage('Item created successfully', MessageBarType.success);
      }

      // Reload data
      await this.loadScheduleData();
    } catch (error) {
      console.error('Error saving item:', error);
      this.showOperationMessage('Failed to save item. Please try again.', MessageBarType.error);
    } finally {
      this.setState({ isFormLoading: false });
    }
  };

  private openDeleteDialog = (item: IOnCallScheduleItem): void => {
    this.setState({
      showDeleteDialog: true,
      itemToDelete: item
    });
  };

  private closeDeleteDialog = (): void => {
    this.setState({
      showDeleteDialog: false,
      itemToDelete: undefined
    });
  };

  private confirmDelete = async (): Promise<void> => {
    if (!this.state.itemToDelete) return;

    try {
      await this.onCallService.deleteOnCallScheduleItem(this.state.itemToDelete.Id);
      this.showOperationMessage('Item deleted successfully', MessageBarType.success);
      this.closeDeleteDialog();
      await this.loadScheduleData();
    } catch (error) {
      console.error('Error deleting item:', error);
      this.showOperationMessage('Failed to delete item. Please try again.', MessageBarType.error);
    }
  };

  private showOperationMessage = (message: string, type: MessageBarType): void => {
    this.setState({
      operationMessage: message,
      operationMessageType: type
    });

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
      this.setState({ operationMessage: '' });
    }, 5000);
  };

  private dismissOperationMessage = (): void => {
    this.setState({ operationMessage: '' });
  };

  private validateDateRange = (startDate: Date | undefined, endDate: Date | undefined): string => {
    if (startDate && endDate && startDate > endDate) {
      return 'End Date cannot be earlier than Start Date.';
    }
    return '';
  };

  private onStartDateChange = (date: Date | null | undefined): void => {
    const newStartDate = date || undefined;
    const { endDate } = this.state.dateRange;

    const validationError = this.validateDateRange(newStartDate, endDate);

    this.setState({
      dateRange: { ...this.state.dateRange, startDate: newStartDate },
      dateValidationError: validationError
    }, () => {
      if (!validationError) {
        void this.loadScheduleData();
      }
    });
  };

  private onEndDateChange = (date: Date | null | undefined): void => {
    const newEndDate = date || undefined;
    const { startDate } = this.state.dateRange;

    const validationError = this.validateDateRange(startDate, newEndDate);

    this.setState({
      dateRange: { ...this.state.dateRange, endDate: newEndDate },
      dateValidationError: validationError
    }, () => {
      if (!validationError) {
        void this.loadScheduleData();
      }
    });
  };

  public render(): React.ReactElement<IOnCallProps> {
    const { hasTeamsContext } = this.props;
    const {
      paginatedItems,
      filteredItems,
      isLoading,
      errorMessage,
      dateValidationError,
      dateRange,
      searchTerm,
      currentPage,
      pageSize,
      totalPages,
      isFiltersExpanded,
      isFormPanelOpen,
      editingItem,
      isFormLoading,
      operationMessage,
      operationMessageType,
      showDeleteDialog,
      itemToDelete
    } = this.state;

    console.log('OnCall component rendering', { filteredItems: filteredItems.length, isLoading, errorMessage });

    // Page size options
    const pageSizeOptions: IDropdownOption[] = [
      { key: 10, text: '10' },
      { key: 20, text: '20' },
      { key: 50, text: '50' }
    ];

    // Responsive stack tokens
    const stackTokens: IStackTokens = { childrenGap: 20 };
    const responsiveStackStyles: IStackStyles = {
      root: {
        backgroundColor: '#fafafa',
        minHeight: '100vh',
        padding: '16px'
      }
    };

    return (
      <section className={`${styles.onCall} ${hasTeamsContext ? styles.teams : ''}`}>
        <Stack tokens={stackTokens} styles={responsiveStackStyles}>
          {/* Header */}
          <Stack
            horizontal
            horizontalAlign="space-between"
            verticalAlign="center"
            wrap
            styles={{
              root: {
                padding: '16px 20px',
                backgroundColor: '#ffffff',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                borderLeft: '4px solid #0078d4',
                '@media (min-width: 768px)': {
                  padding: '20px 30px'
                }
              }
            }}
          >
            <StackItem>
              <Text variant="xxLarge" as="h1" styles={{ root: { color: '#323130', fontWeight: '600', margin: 0 } }}>
                📅 On Call Schedule
              </Text>
            </StackItem>
            <StackItem>
              <PrimaryButton
                text="Add New"
                iconProps={{ iconName: 'Add' }}
                onClick={this.openAddForm}
                styles={{
                  root: {
                    marginTop: '8px',
                    '@media (min-width: 768px)': {
                      marginTop: '0'
                    }
                  }
                }}
              />
            </StackItem>
          </Stack>

          {/* Operation Messages */}
          {operationMessage && (
            <MessageBar
              messageBarType={operationMessageType}
              onDismiss={this.dismissOperationMessage}
              dismissButtonAriaLabel="Close"
              styles={{
                root: {
                  borderRadius: '4px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }
              }}
            >
              {operationMessage}
            </MessageBar>
          )}

          {/* Filters Section - Expandable */}
          <Stack
            styles={{
              root: {
                backgroundColor: '#ffffff',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }
            }}
          >
            {/* Filters Header with Search on Right */}
            <Stack
              horizontal
              horizontalAlign="space-between"
              verticalAlign="center"
              wrap
              styles={{
                root: {
                  padding: '16px 20px',
                  borderBottom: isFiltersExpanded ? '1px solid #edebe9' : 'none',
                  '@media (min-width: 768px)': {
                    padding: '20px 30px'
                  }
                }
              }}
            >
              {/* Left side - Filters toggle */}
              <StackItem grow>
                <Stack
                  horizontal
                  tokens={{ childrenGap: 10 }}
                  verticalAlign="center"
                  onClick={this.toggleFiltersExpanded}
                  styles={{
                    root: {
                      cursor: 'pointer',
                      minWidth: '200px',
                      ':hover': {
                        backgroundColor: '#f8f9fa'
                      },
                      padding: '8px',
                      borderRadius: '4px'
                    }
                  }}
                >
                  <Text variant="large" styles={{ root: { color: '#323130', fontWeight: '600' } }}>
                    🔧 Filters
                  </Text>
                  <Text variant="medium" styles={{ root: { color: '#605e5c' } }}>
                    ({filteredItems.length} items)
                  </Text>
                  {(dateRange.startDate || dateRange.endDate) && (
                    <Text variant="small" styles={{ root: { color: '#0078d4', fontWeight: '600' } }}>
                      (Active)
                    </Text>
                  )}
                  <IconButton
                    iconProps={{ iconName: isFiltersExpanded ? 'ChevronUp' : 'ChevronDown' }}
                    title={isFiltersExpanded ? 'Collapse filters' : 'Expand filters'}
                    styles={{
                      root: { color: '#605e5c' }
                    }}
                  />
                </Stack>
              </StackItem>

              {/* Right side - Search field */}
              <StackItem>
                <Stack tokens={{ childrenGap: 8 }} styles={{ root: { minWidth: '280px', maxWidth: '400px' } }}>
                  <Text variant="medium" as="label" styles={{ root: { fontWeight: '600', color: '#323130' } }}>
                    🔍 Search:
                  </Text>
                  <SearchBox
                    placeholder="Search by Title, Manager, Primary Contact, or Secondary Contact"
                    value={searchTerm}
                    onChange={this.onSearchChange}
                    onClear={this.onClearSearch}
                    styles={{
                      root: {
                        width: '100%',
                        '@media (max-width: 767px)': {
                          width: '280px'
                        }
                      },
                      field: { borderRadius: '4px' }
                    }}
                  />
                </Stack>
              </StackItem>
            </Stack>

            {/* Filters Content - Expandable */}
            {isFiltersExpanded && (
              <Stack
                styles={{
                  root: {
                    padding: '0 30px 25px 30px',
                    borderTop: '1px solid #f3f2f1'
                  }
                }}
              >
                <Stack horizontal tokens={{ childrenGap: 20 }} wrap verticalAlign="end" styles={{ root: { paddingTop: '20px' } }}>
                  {/* Date filters */}
                  <Stack horizontal tokens={{ childrenGap: 20 }} wrap>
                    <Stack tokens={{ childrenGap: 8 }}>
                      <Text variant="medium" as="label" styles={{ root: { fontWeight: '600', color: '#323130' } }}>
                        📅 Start Date:
                      </Text>
                      <DatePicker
                        value={dateRange.startDate}
                        onSelectDate={this.onStartDateChange}
                        placeholder="Select start date"
                        ariaLabel="Start date"
                        styles={{
                          root: {
                            width: 180,
                            '@media (max-width: 767px)': {
                              width: '100%',
                              minWidth: '150px'
                            }
                          },
                          textField: { borderRadius: '4px' }
                        }}
                      />
                    </Stack>

                    <Stack tokens={{ childrenGap: 8 }}>
                      <Text variant="medium" as="label" styles={{ root: { fontWeight: '600', color: '#323130' } }}>
                        📅 End Date:
                      </Text>
                      <DatePicker
                        value={dateRange.endDate}
                        onSelectDate={this.onEndDateChange}
                        placeholder="Select end date"
                        ariaLabel="End date"
                        styles={{
                          root: {
                            width: 180,
                            '@media (max-width: 767px)': {
                              width: '100%',
                              minWidth: '150px'
                            }
                          },
                          textField: { borderRadius: '4px' }
                        }}
                      />
                    </Stack>

                    <Stack tokens={{ childrenGap: 8 }}>
                      <Text variant="medium" as="label" styles={{ root: { color: 'transparent' } }}>
                        &nbsp;
                      </Text>
                      <DefaultButton
                        text="🗑️ Clear Dates"
                        onClick={this.onClearDates}
                        disabled={!dateRange.startDate && !dateRange.endDate}
                        styles={{
                          root: {
                            borderRadius: '4px',
                            border: '1px solid #d1d1d1',
                            '@media (max-width: 767px)': {
                              width: '100%'
                            }
                          },
                          rootHovered: {
                            backgroundColor: '#f3f2f1'
                          }
                        }}
                      />
                    </Stack>
                  </Stack>
                </Stack>
              </Stack>
            )}
          </Stack>

          {/* Validation Error */}
          {dateValidationError && (
            <MessageBar
              messageBarType={MessageBarType.error}
              styles={{
                root: {
                  borderRadius: '4px',
                  boxShadow: '0 2px 8px rgba(168, 0, 0, 0.2)'
                }
              }}
            >
              ⚠️ {dateValidationError}
            </MessageBar>
          )}

          {/* Error Message */}
          {errorMessage && (
            <MessageBar
              messageBarType={MessageBarType.error}
              styles={{
                root: {
                  borderRadius: '4px',
                  boxShadow: '0 2px 8px rgba(168, 0, 0, 0.2)'
                }
              }}
            >
              ❌ {errorMessage}
            </MessageBar>
          )}

          {/* Loading Spinner */}
          {isLoading && (
            <Stack
              horizontalAlign="center"
              styles={{
                root: {
                  padding: '40px',
                  backgroundColor: '#ffffff',
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }
              }}
            >
              <Spinner size={SpinnerSize.large} label="🔄 Loading schedule data..." />
            </Stack>
          )}

          {/* Data Table Section */}
          <Stack
            styles={{
              root: {
                backgroundColor: '#ffffff',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                overflow: 'hidden'
              }
            }}
          >
            {/* Table Header with Page Size Selector */}
            {!isLoading && !errorMessage && filteredItems.length > 0 && (
              <Stack
                horizontal
                horizontalAlign="space-between"
                verticalAlign="center"
                styles={{
                  root: {
                    padding: '15px 25px',
                    backgroundColor: '#f8f9fa',
                    borderBottom: '1px solid #e1e1e1'
                  }
                }}
              >
                <Text variant="mediumPlus" styles={{ root: { fontWeight: '600', color: '#323130' } }}>
                  📊 Schedule Data ({filteredItems.length} {filteredItems.length === 1 ? 'item' : 'items'})
                </Text>

                <Stack horizontal tokens={{ childrenGap: 15 }} verticalAlign="center">
                  <Text variant="small" styles={{ root: { color: '#605e5c' } }}>
                    Showing {Math.min((currentPage - 1) * pageSize + 1, filteredItems.length)} - {Math.min(currentPage * pageSize, filteredItems.length)} of {filteredItems.length} items
                  </Text>
                  <Label styles={{ root: { margin: 0, color: '#323130', fontWeight: '600' } }}>
                    Items per page:
                  </Label>
                  <Dropdown
                    selectedKey={pageSize}
                    onChange={this.onPageSizeChange}
                    options={pageSizeOptions}
                    styles={{
                      dropdown: { width: 80, borderRadius: '4px' },
                      title: { borderRadius: '4px' }
                    }}
                  />
                </Stack>
              </Stack>
            )}

            {/* Data Table */}
            {!isLoading && !errorMessage && (
              <DetailsList
                items={paginatedItems}
                columns={this.columns}
                selectionMode={SelectionMode.none}
                setKey="set"
                layoutMode={1} // DetailsListLayoutMode.justified
                isHeaderVisible={true}
                onRenderMissingItem={() => null}
                styles={{
                  root: {
                    '& .ms-DetailsHeader': {
                      backgroundColor: '#f8f9fa',
                      borderBottom: '2px solid #0078d4'
                    },
                    '& .ms-DetailsHeader-cell': {
                      fontWeight: '600',
                      color: '#323130'
                    },
                    '& .ms-DetailsRow': {
                      borderBottom: '1px solid #f3f2f1'
                    },
                    '& .ms-DetailsRow:hover': {
                      backgroundColor: '#f8f9fa'
                    }
                  }
                }}
              />
            )}

            {/* Pagination Controls */}
            {!isLoading && !errorMessage && totalPages > 1 && (
              <Stack
                horizontal
                tokens={{ childrenGap: 15 }}
                horizontalAlign="center"
                verticalAlign="center"
                styles={{
                  root: {
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderTop: '1px solid #e1e1e1'
                  }
                }}
              >
                <IconButton
                  iconProps={{ iconName: 'ChevronLeft' }}
                  disabled={currentPage === 1}
                  onClick={() => this.onPageChange(currentPage - 1)}
                  title="Previous page"
                  styles={{
                    root: {
                      backgroundColor: currentPage === 1 ? '#f3f2f1' : '#ffffff',
                      border: '1px solid #d1d1d1',
                      borderRadius: '4px'
                    },
                    rootHovered: {
                      backgroundColor: currentPage === 1 ? '#f3f2f1' : '#f8f9fa'
                    }
                  }}
                />
                <Text variant="medium" styles={{ root: { fontWeight: '600', color: '#323130', minWidth: '120px', textAlign: 'center' } }}>
                  Page {currentPage} of {totalPages}
                </Text>
                <IconButton
                  iconProps={{ iconName: 'ChevronRight' }}
                  disabled={currentPage === totalPages}
                  onClick={() => this.onPageChange(currentPage + 1)}
                  title="Next page"
                  styles={{
                    root: {
                      backgroundColor: currentPage === totalPages ? '#f3f2f1' : '#ffffff',
                      border: '1px solid #d1d1d1',
                      borderRadius: '4px'
                    },
                    rootHovered: {
                      backgroundColor: currentPage === totalPages ? '#f3f2f1' : '#f8f9fa'
                    }
                  }}
                />
              </Stack>
            )}
          </Stack>

          {/* No Data Message */}
          {!isLoading && !errorMessage && filteredItems.length === 0 && (
            <Stack
              horizontalAlign="center"
              styles={{
                root: {
                  padding: '60px 40px',
                  backgroundColor: '#ffffff',
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                  textAlign: 'center'
                }
              }}
            >
              <Text variant="xxLarge" styles={{ root: { marginBottom: '15px' } }}>
                📭
              </Text>
              <Text variant="large" styles={{ root: { marginBottom: '10px', fontWeight: '600', color: '#323130' } }}>
                No Schedule Items Found
              </Text>
              <Text variant="medium" styles={{ root: { color: '#605e5c', maxWidth: '400px' } }}>
                {searchTerm || dateRange.startDate || dateRange.endDate
                  ? 'No items match your current filters. Try adjusting your search criteria or date range.'
                  : 'No schedule items are currently available. Please check back later or contact your administrator.'
                }
              </Text>
            </Stack>
          )}
        </Stack>

        {/* Add/Edit Form Panel */}
        <OnCallFormPanel
          isOpen={isFormPanelOpen}
          item={editingItem}
          onDismiss={this.closeForm}
          onSave={this.saveItem}
          isLoading={isFormLoading}
          context={this.props.context}
        />

        {/* Delete Confirmation Dialog */}
        <Dialog
          hidden={!showDeleteDialog}
          onDismiss={this.closeDeleteDialog}
          dialogContentProps={{
            type: DialogType.normal,
            title: 'Confirm Delete',
            subText: `Are you sure you want to delete "${itemToDelete?.Title}"? This action cannot be undone.`
          }}
          modalProps={{
            isBlocking: true,
            styles: { main: { maxWidth: 450 } }
          }}
        >
          <DialogFooter>
            <PrimaryButton
              onClick={this.confirmDelete}
              text="Delete"
              styles={{
                root: { backgroundColor: '#d13438', borderColor: '#d13438' },
                rootHovered: { backgroundColor: '#b71c1c', borderColor: '#b71c1c' }
              }}
            />
            <DefaultButton onClick={this.closeDeleteDialog} text="Cancel" />
          </DialogFooter>
        </Dialog>
      </section>
    );
  }
}
