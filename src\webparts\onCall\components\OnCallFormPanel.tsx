import * as React from 'react';
import {
  Panel,
  PanelType,
  TextField,
  DatePicker,
  PrimaryButton,
  DefaultButton,
  Stack,
  MessageBar,
  MessageBarType} from '@fluentui/react';
import { PeoplePicker, PrincipalType } from '@pnp/spfx-controls-react/lib/PeoplePicker';
import { IOnCallScheduleItem, IPersonField } from './IOnCallScheduleItem';

export interface IOnCallFormPanelProps {
  isOpen: boolean;
  item?: IOnCallScheduleItem;
  onDismiss: () => void;
  onSave: (item: Partial<IOnCallScheduleItem>) => Promise<void>;
  isLoading?: boolean;
  context: any; // WebPartContext for PeoplePicker
}

interface IFormData {
  Title: string;
  StartDate: Date | undefined;
  EndDate: Date | undefined;
  Manager: IPersonField | undefined;
  ManagerMobile: string;
  QueueManager: IPersonField | undefined;
  QueueManagerMobile: string;
  PrimaryContact: IPersonField | undefined;
  PrimaryContactMobile: string;
  Comments: string;
}

interface IFormErrors {
  Title?: string;
  StartDate?: string;
  EndDate?: string;
  dateRange?: string;
}

export const OnCallFormPanel: React.FunctionComponent<IOnCallFormPanelProps> = (props) => {
  const { isOpen, item, onDismiss, onSave, isLoading = false, context } = props;

  const [formData, setFormData] = React.useState<IFormData>({
    Title: '',
    StartDate: undefined,
    EndDate: undefined,
    Manager: undefined,
    ManagerMobile: '',
    QueueManager: undefined,
    QueueManagerMobile: '',
    PrimaryContact: undefined,
    PrimaryContactMobile: '',
    Comments: ''
  });

  const [errors, setErrors] = React.useState<IFormErrors>({});
  const [isSaving, setIsSaving] = React.useState(false);

  // Initialize form data when item changes
  React.useEffect(() => {
    if (item) {
      setFormData({
        Title: item.Title || '',
        StartDate: item.StartDate ? new Date(item.StartDate) : undefined,
        EndDate: item.EndDate ? new Date(item.EndDate) : undefined,
        Manager: item.Manager,
        ManagerMobile: item.ManagerMobile || '',
        QueueManager: item.QueueManager,
        QueueManagerMobile: item.QueueManagerMobile || '',
        PrimaryContact: item.PrimaryContact,
        PrimaryContactMobile: item.PrimaryContactMobile || '',
        Comments: item.Comments || ''
      });
    } else {
      // Reset form for new item
      setFormData({
        Title: '',
        StartDate: undefined,
        EndDate: undefined,
        Manager: undefined,
        ManagerMobile: '',
        QueueManager: undefined,
        QueueManagerMobile: '',
        PrimaryContact: undefined,
        PrimaryContactMobile: '',
        Comments: ''
      });
    }
    setErrors({});
  }, [item, isOpen]);

  const validateForm = (): boolean => {
    const newErrors: IFormErrors = {};

    // Title is required
    if (!formData.Title.trim()) {
      newErrors.Title = 'Title is required';
    }

    // Start date is required
    if (!formData.StartDate) {
      newErrors.StartDate = 'Start date is required';
    }

    // End date is required
    if (!formData.EndDate) {
      newErrors.EndDate = 'End date is required';
    }

    // End date must not be before start date
    if (formData.StartDate && formData.EndDate && formData.EndDate < formData.StartDate) {
      newErrors.dateRange = 'End date must not be before start date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async (): Promise<void> => {
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      const itemToSave: Partial<IOnCallScheduleItem> = {
        Title: formData.Title.trim(),
        StartDate: formData.StartDate?.toISOString(),
        EndDate: formData.EndDate?.toISOString(),
        Manager: formData.Manager,
        ManagerMobile: formData.ManagerMobile.trim(),
        QueueManager: formData.QueueManager,
        QueueManagerMobile: formData.QueueManagerMobile.trim(),
        PrimaryContact: formData.PrimaryContact,
        PrimaryContactMobile: formData.PrimaryContactMobile.trim(),
        Comments: formData.Comments.trim()
      };

      await onSave(itemToSave);
      onDismiss();
    } catch (error) {
      console.error('Error saving item:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleFieldChange = (field: keyof IFormData, value: any): void => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear related errors when user starts typing
    if (errors[field as keyof IFormErrors]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }

    // Clear date range error when either date changes
    if ((field === 'StartDate' || field === 'EndDate') && errors.dateRange) {
      setErrors(prev => ({
        ...prev,
        dateRange: undefined
      }));
    }
  };

  // PeoplePicker change handlers
  const handleManagerChange = (items: any[]): void => {
    const manager = items.length > 0 ? {
      Title: items[0].text,
      EMail: items[0].secondaryText || items[0].optionalText,
      Id: items[0].id ? parseInt(items[0].id) : undefined,
      LoginName: items[0].loginName || items[0].id
    } : undefined;
    handleFieldChange('Manager', manager);
  };

  const handleQueueManagerChange = (items: any[]): void => {
    const queueManager = items.length > 0 ? {
      Title: items[0].text,
      EMail: items[0].secondaryText || items[0].optionalText,
      Id: items[0].id ? parseInt(items[0].id) : undefined,
      LoginName: items[0].loginName || items[0].id
    } : undefined;
    handleFieldChange('QueueManager', queueManager);
  };

  const handlePrimaryContactChange = (items: any[]): void => {
    const primaryContact = items.length > 0 ? {
      Title: items[0].text,
      EMail: items[0].secondaryText || items[0].optionalText,
      Id: items[0].id ? parseInt(items[0].id) : undefined,
      LoginName: items[0].loginName || items[0].id
    } : undefined;
    handleFieldChange('PrimaryContact', primaryContact);
  };

  // Note: People picker functionality can be added later if needed
  // const suggestionProps: IBasePickerSuggestionsProps = {
  //   suggestionsHeaderText: 'Suggested People',
  //   mostRecentlyUsedHeaderText: 'Suggested Contacts',
  //   noResultsFoundText: 'No results found',
  //   loadingText: 'Loading',
  //   showRemoveButtons: true,
  //   suggestionsAvailableAlertText: 'People Picker Suggestions available',
  //   suggestionsContainerAriaLabel: 'Suggested contacts'
  // };

  return (
    <Panel
      headerText={item ? 'Edit On-Call Schedule' : 'Add New On-Call Schedule'}
      isOpen={isOpen}
      onDismiss={onDismiss}
      type={PanelType.medium}
      closeButtonAriaLabel="Close"
      isFooterAtBottom={true}
      onRenderFooterContent={() => (
        <Stack horizontal tokens={{ childrenGap: 8 }}>
          <PrimaryButton
            text="Save"
            onClick={handleSave}
            disabled={isSaving || isLoading}
          />
          <DefaultButton
            text="Cancel"
            onClick={onDismiss}
            disabled={isSaving || isLoading}
          />
        </Stack>
      )}
    >
      <Stack tokens={{ childrenGap: 16 }}>
        {/* Date Range Error */}
        {errors.dateRange && (
          <MessageBar messageBarType={MessageBarType.error}>
            {errors.dateRange}
          </MessageBar>
        )}

        {/* Title Field */}
        <TextField
          label="Title"
          required
          value={formData.Title}
          onChange={(_, newValue) => handleFieldChange('Title', newValue || '')}
          errorMessage={errors.Title}
          disabled={isSaving || isLoading}
        />

        {/* Date Fields */}
        <Stack horizontal tokens={{ childrenGap: 16 }}>
          <Stack.Item grow>
            <DatePicker
              label="Start Date"
              isRequired
              value={formData.StartDate}
              onSelectDate={(date) => handleFieldChange('StartDate', date)}
              placeholder="Select start date"
              ariaLabel="Start date"
              disabled={isSaving || isLoading}
            />
            {errors.StartDate && (
              <div style={{ color: '#a4262c', fontSize: '12px', marginTop: '5px' }}>
                {errors.StartDate}
              </div>
            )}
          </Stack.Item>
          <Stack.Item grow>
            <DatePicker
              label="End Date"
              isRequired
              value={formData.EndDate}
              onSelectDate={(date) => handleFieldChange('EndDate', date)}
              placeholder="Select end date"
              ariaLabel="End date"
              disabled={isSaving || isLoading}
            />
            {errors.EndDate && (
              <div style={{ color: '#a4262c', fontSize: '12px', marginTop: '5px' }}>
                {errors.EndDate}
              </div>
            )}
          </Stack.Item>
        </Stack>

        {/* Manager Section */}
        <Stack tokens={{ childrenGap: 8 }}>
          <PeoplePicker
            context={context}
            titleText="Manager"
            personSelectionLimit={1}
            groupName=""
            showtooltip={true}
            disabled={isSaving || isLoading}
            onChange={handleManagerChange}
            defaultSelectedUsers={formData.Manager ? [formData.Manager.LoginName || formData.Manager.EMail || formData.Manager.Title] : []}

            principalTypes={[PrincipalType.User]}
            resolveDelay={1000}
            ensureUser={true}
            suggestionsLimit={15}
            placeholder="Search for a user..."
          />
          <TextField
            label="Manager Mobile"
            value={formData.ManagerMobile}
            onChange={(_, newValue) => handleFieldChange('ManagerMobile', newValue || '')}
            disabled={isSaving || isLoading}
          />
        </Stack>

        {/* Queue Manager Section */}
        <Stack tokens={{ childrenGap: 8 }}>
          <PeoplePicker
            context={context}
            titleText="Secondary Contact"
            personSelectionLimit={1}
            groupName=""
            showtooltip={true}
            disabled={isSaving || isLoading}
            onChange={handleQueueManagerChange}
            defaultSelectedUsers={formData.QueueManager ? [formData.QueueManager.LoginName || formData.QueueManager.EMail || formData.QueueManager.Title] : []}

            principalTypes={[PrincipalType.User]}
            resolveDelay={1000}
            ensureUser={true}
            suggestionsLimit={15}
            placeholder="Search for a user..."
          />
          <TextField
            label="Secondary Contact Mobile"
            value={formData.QueueManagerMobile}
            onChange={(_, newValue) => handleFieldChange('QueueManagerMobile', newValue || '')}
            disabled={isSaving || isLoading}
          />
        </Stack>

        {/* Primary Contact Section */}
        <Stack tokens={{ childrenGap: 8 }}>
          <PeoplePicker
            context={context}
            titleText="Primary Contact"
            personSelectionLimit={1}
            groupName=""
            showtooltip={true}
            disabled={isSaving || isLoading}
            onChange={handlePrimaryContactChange}
            defaultSelectedUsers={formData.PrimaryContact ? [formData.PrimaryContact.LoginName || formData.PrimaryContact.EMail || formData.PrimaryContact.Title] : []}

            principalTypes={[PrincipalType.User]}
            resolveDelay={1000}
            ensureUser={true}
            suggestionsLimit={15}
            placeholder="Search for a user..."
          />
          <TextField
            label="Primary Contact Mobile"
            value={formData.PrimaryContactMobile}
            onChange={(_, newValue) => handleFieldChange('PrimaryContactMobile', newValue || '')}
            disabled={isSaving || isLoading}
          />
        </Stack>

        {/* Comments */}
        <TextField
          label="Comments"
          multiline
          rows={3}
          value={formData.Comments}
          onChange={(_, newValue) => handleFieldChange('Comments', newValue || '')}
          disabled={isSaving || isLoading}
        />
      </Stack>
    </Panel>
  );
};
