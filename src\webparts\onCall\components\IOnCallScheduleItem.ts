export interface IPersonField {
  Title: string;
  EMail?: string;
  Id?: number;
  LoginName?: string;
}

export interface IOnCallScheduleItem {
  Id: number;
  Title: string;
  Manager?: IPersonField;
  ManagerMobile?: string;
  QueueManager?: IPersonField;
  QueueManagerMobile?: string;
  StartDate: string;
  EndDate: string;
  PrimaryContact?: IPersonField;
  PrimaryContactMobile?: string;
  Comments?: string;
  Created: string;
  Modified: string;
}

export interface IOnCallScheduleResponse {
  value: IOnCallScheduleItem[];
}

export interface IDateRange {
  startDate: Date | undefined;
  endDate: Date | undefined;
}
